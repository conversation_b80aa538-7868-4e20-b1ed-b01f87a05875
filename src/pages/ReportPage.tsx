import { useParams } from "react-router-dom";
import { useState } from "react";
import { BackGround } from "@/components/BackGround";
import logo from "@/assets/adeptos_logo.png";

// Mock data interface
interface ReportData {
  businessName: string;
  creationDate: string;
  aiReadinessScore: number;
  scoreMessage: string;
  projectedImpact: {
    type: "savings" | "time";
    value: string;
  };
  wasteAreas: string[];
  opportunities: {
    title: string;
    description: string;
  }[];
}

// Mock data generator based on report ID
const generateMockData = (id: string): ReportData => {
  const businessNames = [
    "TechFlow Solutions",
    "Green Valley Consulting",
    "Digital Marketing Pro",
    "Innovative Healthcare",
    "Smart Retail Systems"
  ];

  const scoreMessages = [
    "You are positioned well to implement AI",
    "Your business shows strong AI readiness potential",
    "Excellent foundation for AI transformation",
    "Good positioning for AI implementation",
    "Strong potential for AI-driven growth"
  ];

  // Use ID to generate consistent data
  const idNum = parseInt(id) || 1;
  const businessIndex = (idNum - 1) % businessNames.length;
  const score = 45 + (idNum % 40); // Score between 45-85

  return {
    businessName: businessNames[businessIndex],
    creationDate: new Date(Date.now() - (idNum * 24 * 60 * 60 * 1000)).toLocaleDateString(),
    aiReadinessScore: score,
    scoreMessage: scoreMessages[businessIndex],
    projectedImpact: idNum % 2 === 0
      ? { type: "savings", value: "$18,500" }
      : { type: "time", value: "780 Hours/Year" },
    wasteAreas: [
      "Sales",
      "Customer service",
      "Administrative tasks",
      "Data entry",
      "Inventory management"
    ].slice(0, 2 + (idNum % 3)),
    opportunities: [
      {
        title: "Customer Support Automation",
        description: "Based on your challenges, an AI-powered chatbot could handle ~70% of initial customer inquiries, freeing up your team and providing 24/7 support."
      },
      {
        title: "Lead Nurturing Automation",
        description: "For your ideal customer, an automated AI email sequence can nurture leads from initial contact to sales-ready, increasing conversion rates by an estimated 25%."
      },
      {
        title: "Operational Efficiency",
        description: "Automating your client onboarding process can reduce manual work by 15 hours per week and decrease new client churn."
      }
    ]
  };
};

export function ReportPage() {
  const { id } = useParams();
  const [isDownloading, setIsDownloading] = useState(false);

  if (!id) {
    return (
      <div className="min-h-screen bg-white dark:bg-dark theme-transition flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-title font-bold text-dark dark:text-white theme-transition mb-4">
            Report Not Found
          </h1>
          <p className="text-gray dark:text-light font-body">
            The requested report could not be found.
          </p>
        </div>
      </div>
    );
  }

  const reportData = generateMockData(id);

  const handleDownload = async () => {
    setIsDownloading(true);
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsDownloading(false);

    alert("Download functionality will be implemented when the API is ready.");
  };

  const handleImplementationPlan = () => {
    alert("Implementation plan booking will be available when the API is ready.");
  };

  return (
    <div className="report-container relative min-h-screen overflow-x-hidden bg-white dark:bg-dark theme-transition">
      {/* Background */}
      <div className="absolute inset-0 z-0">
        <BackGround />
      </div>

      {/* Header */}
      <header className="fixed top-0 left-0 right-0 z-40 backdrop-blur-lg theme-transition">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3 sm:py-4 h-14 sm:h-16 md:h-20">
            {/* Left: Logo */}
            <div className="flex items-center">
              <img
                src={logo}
                alt="Adeptos AI Logo"
                className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 lg:w-16 lg:h-16 drop-shadow-sm hover:scale-105 transition-transform duration-200"
              />
              <span className="ml-2 sm:ml-3 text-sm sm:text-base md:text-lg lg:text-xl font-brand font-bold text-dark dark:text-white theme-transition">
                adeptos
                <span className="text-gray dark:text-gray">.ai</span>
              </span>
            </div>

            {/* Right: Report Title */}
            <div className="text-xs sm:text-sm md:text-base font-body text-dark dark:text-white theme-transition">
              <span className="font-semibold">AI Readiness Report</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 pt-20 sm:pt-24 md:pt-28 lg:pt-32 px-4 sm:px-6 lg:px-8 pb-8 min-h-screen">
        <div className="max-w-4xl mx-auto space-y-8">

          {/* Business Header Section */}
          <div className="text-center space-y-4">
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-title font-bold text-dark dark:text-white theme-transition leading-tight">
              {reportData.businessName}
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl font-body text-dark dark:text-white theme-transition">
              Report created on {reportData.creationDate}
            </p>
          </div>

          {/* AI Readiness Score Section */}
          <div className="bg-card-bg backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
            <div className="text-center space-y-4">
              <div className="inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-btn-bg theme-transition">
                <span className="text-3xl sm:text-4xl font-title font-bold text-btn-text theme-transition">
                  {reportData.aiReadinessScore}
                </span>
                <span className="text-lg sm:text-xl font-title text-btn-text theme-transition">
                  /100
                </span>
              </div>
              <h2 className="text-xl sm:text-2xl md:text-3xl font-title font-bold text-card-text theme-transition">
                AI Readiness Score
              </h2>
              <p className="text-lg sm:text-xl text-card-label font-body theme-transition">
                {reportData.scoreMessage}
              </p>
            </div>
          </div>

          {/* Projected Impact Section */}
          <div className="bg-card-bg backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
            <div className="text-center space-y-4">
              <h3 className="text-xl sm:text-2xl font-title font-bold text-card-text theme-transition">
                Projected Impact
              </h3>
              <div className="text-2xl sm:text-3xl font-title font-bold text-card-text theme-transition">
                {reportData.projectedImpact.type === "savings"
                  ? `Estimated Annual Savings/Gain: ${reportData.projectedImpact.value}`
                  : `Estimated Time Reclaimed: ${reportData.projectedImpact.value}`
                }
              </div>
            </div>
          </div>

          {/* Download Button */}
          <div className="text-center">
            <button
              onClick={handleDownload}
              disabled={isDownloading}
              className="
                px-8 sm:px-12 md:px-16 py-4 sm:py-5
                text-lg sm:text-xl font-body font-bold
                bg-btn-bg
                text-btn-text
                rounded-full
                transition-all duration-300
                hover:scale-105
                shadow-lg hover:shadow-xl
                theme-transition
                backdrop-blur-sm
                focus:outline-none
                focus:ring-2
                focus:ring-dark/50 dark:focus:ring-light/50
                focus:ring-offset-2
                focus:ring-offset-transparent
                disabled:opacity-50
                disabled:cursor-not-allowed
                disabled:hover:scale-100
                min-h-[56px] sm:min-h-[64px]
                w-full max-w-md mx-auto
                hover:bg-btn-hover
              "
            >
              {isDownloading ? "Preparing Download..." : "Download Report"}
            </button>
          </div>

          {/* Waste Identification Section */}
          <div className="bg-card-bg backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
            <h3 className="text-xl sm:text-2xl font-title font-bold text-card-text theme-transition mb-6">
              Biggest Waste Identified in:
            </h3>
            <ol className="space-y-3">
              {reportData.wasteAreas.map((area, index) => (
                <li key={index} className="flex items-center space-x-3">
                  <span className="flex-shrink-0 w-8 h-8 bg-btn-bg rounded-full flex items-center justify-center text-btn-text font-title font-bold theme-transition">
                    {index + 1}
                  </span>
                  <span className="text-lg font-body text-card-text theme-transition capitalize">
                    {area}
                  </span>
                </li>
              ))}
            </ol>
          </div>

          {/* AI Opportunities Section */}
          <div className="bg-card-bg backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
            <h3 className="text-xl sm:text-2xl font-title font-bold text-card-text theme-transition mb-6">
              Top 3 AI Automation Opportunities
            </h3>
            <div className="space-y-6">
              {reportData.opportunities.map((opportunity, index) => (
                <div key={index} className="bg-white/60 dark:bg-gray/40 rounded-xl p-6 border border-gray/10 dark:border-light/10 theme-transition">
                  <div className="flex items-start space-x-4">
                    <span className="flex-shrink-0 w-10 h-10 bg-btn-bg rounded-full flex items-center justify-center text-btn-text font-title font-bold theme-transition">
                      {index + 1}
                    </span>
                    <div className="flex-1">
                      <h4 className="text-lg sm:text-xl font-title font-bold text-card-text theme-transition mb-3">
                        {opportunity.title}
                      </h4>
                      <p className="text-base font-body text-card-label theme-transition leading-relaxed">
                        {opportunity.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Footer Section */}
          <div className="bg-card-bg backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
            <div className="text-center space-y-6">
              <p className="text-lg sm:text-xl font-body text-card-text theme-transition leading-relaxed max-w-3xl mx-auto">
                This is your starting point. You now know WHAT is possible. But knowing is not the same as doing.
                The difference between success and failure is in the execution.
              </p>
              <button
                onClick={handleImplementationPlan}
                className="
                  px-8 sm:px-12 md:px-16 py-4 sm:py-5
                  text-lg sm:text-xl font-body font-bold
                  bg-btn-bg
                  text-btn-text
                  rounded-full
                  transition-all duration-300
                  hover:scale-105
                  shadow-lg hover:shadow-xl
                  theme-transition
                  backdrop-blur-sm
                  focus:outline-none
                  focus:ring-2
                  focus:ring-dark/50 dark:focus:ring-light/50
                  focus:ring-offset-2
                  focus:ring-offset-transparent
                  min-h-[56px] sm:min-h-[64px]
                  max-w-2xl mx-auto
                  w-full sm:w-auto
                  hover:bg-btn-hover
                "
              >
                Unlock Full Implementation Plan & 1 on 1 Strategy Call with an AI Expert
              </button>
            </div>
          </div>

        </div>
      </main>
    </div>
  );
}