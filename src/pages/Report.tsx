import { useParams } from "react-router-dom";
import { useState } from "react";

// Mock data interface
interface ReportData {
  businessName: string;
  creationDate: string;
  aiReadinessScore: number;
  scoreMessage: string;
  projectedImpact: {
    type: "savings" | "time";
    value: string;
  };
  wasteAreas: string[];
  opportunities: {
    title: string;
    description: string;
  }[];
}

// Mock data generator based on report ID
const generateMockData = (id: string): ReportData => {
  const businessNames = [
    "TechFlow Solutions",
    "Green Valley Consulting",
    "Digital Marketing Pro",
    "Innovative Healthcare",
    "Smart Retail Systems"
  ];

  const scoreMessages = [
    "You are positioned well to implement AI",
    "Your business shows strong AI readiness potential",
    "Excellent foundation for AI transformation",
    "Good positioning for AI implementation",
    "Strong potential for AI-driven growth"
  ];

  // Use ID to generate consistent data
  const idNum = parseInt(id) || 1;
  const businessIndex = (idNum - 1) % businessNames.length;
  const score = 45 + (idNum % 40); // Score between 45-85

  return {
    businessName: businessNames[businessIndex],
    creationDate: new Date(Date.now() - (idNum * 24 * 60 * 60 * 1000)).toLocaleDateString(),
    aiReadinessScore: score,
    scoreMessage: scoreMessages[businessIndex],
    projectedImpact: idNum % 2 === 0
      ? { type: "savings", value: "$18,500" }
      : { type: "time", value: "780 Hours/Year" },
    wasteAreas: [
      "Sales",
      "Customer service",
      "Administrative tasks",
      "Data entry",
      "Inventory management"
    ].slice(0, 2 + (idNum % 3)),
    opportunities: [
      {
        title: "Customer Support Automation",
        description: "Based on your challenges, an AI-powered chatbot could handle ~70% of initial customer inquiries, freeing up your team and providing 24/7 support."
      },
      {
        title: "Lead Nurturing Automation",
        description: "For your ideal customer, an automated AI email sequence can nurture leads from initial contact to sales-ready, increasing conversion rates by an estimated 25%."
      },
      {
        title: "Operational Efficiency",
        description: "Automating your client onboarding process can reduce manual work by 15 hours per week and decrease new client churn."
      }
    ]
  };
};

export function ReportPage() {
  const { id } = useParams();
  const [isDownloading, setIsDownloading] = useState(false);

  if (!id) {
    return (
      <div className="min-h-screen bg-white dark:bg-dark theme-transition flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-title font-bold text-dark dark:text-white theme-transition mb-4">
            Report Not Found
          </h1>
          <p className="text-gray dark:text-light font-body">
            The requested report could not be found.
          </p>
        </div>
      </div>
    );
  }

  const reportData = generateMockData(id);

  const handleDownload = async () => {
    setIsDownloading(true);
    // Simulate download process
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsDownloading(false);

    // In a real implementation, this would trigger a PDF download
    alert("Download functionality will be implemented when the API is ready.");
  };

  const handleImplementationPlan = () => {
    // In a real implementation, this would navigate to a booking system
    alert("Implementation plan booking will be available when the API is ready.");
  };

  return (
    <div className="min-h-screen bg-white dark:bg-dark theme-transition">
      {/* Header Section */}
      <div className="bg-white/80 dark:bg-gray/20 backdrop-blur-sm border-b border-gray/20 dark:border-light/20 theme-transition">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center space-y-2">
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-title font-bold text-dark dark:text-white theme-transition">
              {reportData.businessName}
            </h1>
            <p className="text-sm sm:text-base text-gray dark:text-light font-body theme-transition">
              Report created on {reportData.creationDate}
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">

        {/* AI Readiness Score Section */}
        <div className="bg-white/80 dark:bg-gray/20 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
          <div className="text-center space-y-4">
            <div className="inline-flex items-center justify-center w-24 h-24 sm:w-32 sm:h-32 rounded-full bg-dark dark:bg-light theme-transition">
              <span className="text-3xl sm:text-4xl font-title font-bold text-white dark:text-dark theme-transition">
                {reportData.aiReadinessScore}
              </span>
              <span className="text-lg sm:text-xl font-title text-white dark:text-dark theme-transition">
                /100
              </span>
            </div>
            <h2 className="text-xl sm:text-2xl md:text-3xl font-title font-bold text-dark dark:text-white theme-transition">
              AI Readiness Score
            </h2>
            <p className="text-lg sm:text-xl text-gray dark:text-light font-body theme-transition">
              {reportData.scoreMessage}
            </p>
          </div>
        </div>

        {/* Projected Impact Section */}
        <div className="bg-white/80 dark:bg-gray/20 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
          <div className="text-center space-y-4">
            <h3 className="text-xl sm:text-2xl font-title font-bold text-dark dark:text-white theme-transition">
              Projected Impact
            </h3>
            <div className="text-2xl sm:text-3xl font-title font-bold text-dark dark:text-white theme-transition">
              {reportData.projectedImpact.type === "savings"
                ? `Estimated Annual Savings/Gain: ${reportData.projectedImpact.value}`
                : `Estimated Time Reclaimed: ${reportData.projectedImpact.value}`
              }
            </div>
          </div>
        </div>

        {/* Download Button */}
        <div className="text-center">
          <button
            onClick={handleDownload}
            disabled={isDownloading}
            className="
              px-8 sm:px-12 py-4 sm:py-5
              text-lg sm:text-xl font-body font-bold
              bg-dark dark:bg-light
              text-white dark:text-dark
              rounded-full
              transition-all duration-300
              hover:scale-105
              shadow-lg hover:shadow-xl
              theme-transition
              backdrop-blur-sm
              focus:outline-none
              focus:ring-2
              focus:ring-dark/50 dark:focus:ring-light/50
              focus:ring-offset-2
              focus:ring-offset-transparent
              disabled:opacity-50
              disabled:cursor-not-allowed
              disabled:hover:scale-100
            "
          >
            {isDownloading ? "Preparing Download..." : "Download Report"}
          </button>
        </div>

        {/* Waste Identification Section */}
        <div className="bg-white/80 dark:bg-gray/20 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
          <h3 className="text-xl sm:text-2xl font-title font-bold text-dark dark:text-white theme-transition mb-6">
            Biggest Waste Identified in:
          </h3>
          <ol className="space-y-3">
            {reportData.wasteAreas.map((area, index) => (
              <li key={index} className="flex items-center space-x-3">
                <span className="flex-shrink-0 w-8 h-8 bg-dark dark:bg-light rounded-full flex items-center justify-center text-white dark:text-dark font-title font-bold theme-transition">
                  {index + 1}
                </span>
                <span className="text-lg font-body text-dark dark:text-white theme-transition capitalize">
                  {area}
                </span>
              </li>
            ))}
          </ol>
        </div>

        {/* AI Opportunities Section */}
        <div className="bg-white/80 dark:bg-gray/20 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
          <h3 className="text-xl sm:text-2xl font-title font-bold text-dark dark:text-white theme-transition mb-6">
            Top 3 AI Automation Opportunities
          </h3>
          <div className="space-y-6">
            {reportData.opportunities.map((opportunity, index) => (
              <div key={index} className="bg-white/60 dark:bg-gray/40 rounded-xl p-6 border border-gray/10 dark:border-light/10 theme-transition">
                <div className="flex items-start space-x-4">
                  <span className="flex-shrink-0 w-10 h-10 bg-dark dark:bg-light rounded-full flex items-center justify-center text-white dark:text-dark font-title font-bold theme-transition">
                    {index + 1}
                  </span>
                  <div className="flex-1">
                    <h4 className="text-lg sm:text-xl font-title font-bold text-dark dark:text-white theme-transition mb-3">
                      {opportunity.title}
                    </h4>
                    <p className="text-base font-body text-gray dark:text-light theme-transition leading-relaxed">
                      {opportunity.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Footer Section */}
        <div className="bg-white/80 dark:bg-gray/20 backdrop-blur-sm rounded-2xl p-6 sm:p-8 border border-gray/20 dark:border-light/20 theme-transition">
          <div className="text-center space-y-6">
            <p className="text-lg sm:text-xl font-body text-dark dark:text-white theme-transition leading-relaxed max-w-3xl mx-auto">
              This is your starting point. You now know WHAT is possible. But knowing is not the same as doing.
              The difference between success and failure is in the execution.
            </p>
            <button
              onClick={handleImplementationPlan}
              className="
                px-8 sm:px-12 py-4 sm:py-5
                text-lg sm:text-xl font-body font-bold
                bg-dark dark:bg-light
                text-white dark:text-dark
                rounded-full
                transition-all duration-300
                hover:scale-105
                shadow-lg hover:shadow-xl
                theme-transition
                backdrop-blur-sm
                focus:outline-none
                focus:ring-2
                focus:ring-dark/50 dark:focus:ring-light/50
                focus:ring-offset-2
                focus:ring-offset-transparent
                max-w-2xl mx-auto
                w-full sm:w-auto
              "
            >
              Unlock Full Implementation Plan & 1 on 1 Strategy Call with an AI Expert
            </button>
          </div>
        </div>

      </div>
    </div>
  );
}